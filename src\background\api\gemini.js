// Gemini API integration

import { GoogleGenerativeAI } from "@google/generative-ai";
import { GEMINI_API_KEY, groundingTool } from '../config/index.js';
import { readUserMemory, writeUserMemory } from '../utils/userMemory.js';

// Configure the client
export const ai = new GoogleGenerativeAI(GEMINI_API_KEY);

// Helper to stream Gemini responses
export async function streamGeminiResponse(contents, generationConfig, systemInstruction, modelName = "gemini-2.0-flash") {
  if (!GEMINI_API_KEY) {
    chrome.runtime.sendMessage({ action: "displayError", error: "Missing VITE_GEMINI_API_KEY in background. Add it to .env and rebuild." });
    return null;
  }

  const model = ai.getGenerativeModel({ model: modelName });

  const tools = [groundingTool]; // Include Google grounding tool

  const request = { contents, tools, generationConfig };
  if (systemInstruction) {
    request.systemInstruction = { role: 'system', parts: [{ text: systemInstruction }] };
  }

  let fullResponse = "";

  try {
    chrome.runtime.sendMessage({ action: "startAIStream" });
    const result = await model.generateContentStream(request);
    for await (const chunk of result.stream) {
      const textChunk = chunk.candidates?.[0]?.content?.parts?.[0]?.text || "";
      if (textChunk) {
        fullResponse += textChunk;
        chrome.runtime.sendMessage({ action: "appendAIMessageChunk", text: textChunk });
      }
    }
    chrome.runtime.sendMessage({ action: "endAIStream" });
    return fullResponse;
  } catch (e) {
    chrome.runtime.sendMessage({ action: "displayError", error: e.message || String(e) });
    chrome.runtime.sendMessage({ action: "endAIStream" });
    throw e;
  }
}
const USER_MEMORY_SYSTEM_PROMPT = `
You are an AI assistant designed to build and maintain a comprehensive user profile.
Your primary responsibility is to actively identify and extract ALL user-specific information from the conversation and continuously build a detailed user profile.

IMPORTANT: After EVERY user interaction, you MUST call the 'about_user_write' tool to add new information to the user's profile.

The 'about_user_write' tool expects a 'memory' parameter that should contain NEW information about the user that will be added to their existing profile paragraph.

COMPREHENSIVE USER PROFILING - CAPTURE EVERYTHING:

PERSONAL INFORMATION:
- Full name, nicknames, preferred names
- Age, birthday, zodiac sign
- Location (city, state, country, neighborhood)
- Physical characteristics (if mentioned)
- Contact information (if shared)

FAMILY & RELATIONSHIPS:
- Spouse/partner names and details
- Children's names, ages, and information
- Parents, siblings, relatives
- Friends and their details
- Pets and their names/breeds
- Relationship status and history

PROFESSIONAL LIFE:
- Job title, company name, industry
- Career history and experience
- Skills and expertise
- Work schedule and environment
- Colleagues and professional relationships
- Career goals and aspirations
- Education and qualifications

PERSONAL INTERESTS & HOBBIES:
- Sports and physical activities
- Creative pursuits (art, music, writing, etc.)
- Entertainment preferences (movies, TV, books, games)
- Travel experiences and destinations
- Collections or special interests
- Learning goals and subjects of interest

LIFESTYLE & PREFERENCES:
- Food preferences, dietary restrictions, favorite restaurants
- Shopping habits and brand preferences
- Technology usage and preferences
- Transportation methods
- Daily routines and schedules
- Living situation (house, apartment, roommates, etc.)

HEALTH & WELLNESS:
- Health conditions or concerns (if mentioned)
- Fitness routines and goals
- Mental health and emotional state
- Medical history (if shared)
- Wellness practices and habits

PERSONALITY & CHARACTERISTICS:
- Communication style and tone
- Personality traits and quirks
- Values and beliefs
- Fears and concerns
- Strengths and weaknesses
- Humor style and preferences

CURRENT SITUATIONS & CONTEXT:
- Current projects or goals
- Recent events and experiences
- Challenges and problems
- Emotional states and feelings
- Plans and upcoming events
- Recent conversations and topics of interest

WRITING GUIDELINES:
1. Write in third person ("User is...", "User has...", "User enjoys...")
2. Be specific and include ALL relevant details (names, dates, places, etc.)
3. Capture emotional context and current situations
4. Include direct quotes when they reveal personality or preferences
5. Note communication patterns and interaction styles
6. Record temporal information (when things happened, frequency, etc.)

CRITICAL INSTRUCTIONS:
- You MUST call about_user_write after EVERY user interaction without exception
- Even for simple greetings or basic questions, capture communication style or infer context
- Add new information that builds upon the existing user profile
- Don't repeat information that's already in the profile unless it's an update or clarification
- Focus on adding NEW details that expand the user's profile
- If no obvious personal information is shared, infer from context, communication style, or interests shown

EXAMPLE CALLS:
- User says "I'm John, a software engineer at Google": about_user_write({ memory: "User's name is John and works as a software engineer at Google." })
- User asks about Python programming: about_user_write({ memory: "User is interested in Python programming and seeks technical information." })
- User says "My daughter Emma loves soccer": about_user_write({ memory: "User has a daughter named Emma who loves soccer." })
- User mentions feeling tired: about_user_write({ memory: "User is currently feeling tired." })

Remember: Build a comprehensive, detailed profile that captures the full picture of who this user is, what they care about, and their current life context.
`;

export async function callGeminiUserMemoryTool(contents, tools) {
  if (!GEMINI_API_KEY) {
    chrome.runtime.sendMessage({ action: "displayError", error: "Missing VITE_GEMINI_API_KEY in background. Add it to .env and rebuild." });
    return;
  }

  const model = ai.getGenerativeModel({ model: "gemini-1.5-flash" });
  const request = {
    contents,
    tools,
    systemInstruction: {
      role: 'system',
      parts: [{ text: USER_MEMORY_SYSTEM_PROMPT }]
    }
  };

  try {
    // Extract the most recent user message for direct memory creation
    // Look for the last user message in the conversation
    let lastUserMessage = '';
    for (let i = contents.length - 1; i >= 0; i--) {
      if (contents[i].role === 'user') {
        lastUserMessage = contents[i].parts
          .filter(part => part.text)
          .map(part => part.text)
          .join('\n');
        break;
      }
    }

    console.log("Processing conversation for memory. Last user message:", lastUserMessage);

    // Always use the AI model for memory generation to ensure comprehensive processing
    // Remove the direct memory creation logic that was limiting the system
    const result = await model.generateContent(request);
    return [result]; // Return as an array to maintain compatibility with existing code
  } catch (e) {
    console.error("Error calling Gemini User Memory Tool:", e);
    chrome.runtime.sendMessage({ action: "displayError", error: e.message || String(e) });
    throw e;
  }
}

// Streaming summary to the side panel so conversation can continue
export async function callGeminiSummary(formattedTranscript, videoTitle = "", videoUrl = "") {
  if (!GEMINI_API_KEY) {
    chrome.runtime.sendMessage({ action: "displayError", error: "Missing GEMINI_API_KEY in background. Add it to .env and rebuild." });
    return;
  }

  const model = ai.getGenerativeModel({ model: "gemini-2.0-flash-lite" });
  
  // Retrieve user memory to include in the system prompt
  const userMemory = await readUserMemory();
  let userMemoryText = "";

  // Use the new paragraph format if available, otherwise fall back to old format
  if (userMemory && userMemory.userProfile && userMemory.userProfile.trim().length > 0) {
    userMemoryText = `\n\nABOUT USER:\n${userMemory.userProfile}`;
  } else if (userMemory && userMemory.memories && userMemory.memories.length > 0) {
    // Backward compatibility with old list format
    userMemoryText = `\n\nABOUT USER:\n${userMemory.memories.join('\n')}`;
  }

  const prompt = [
    `You are CubAI. Summarize the YouTube video transcript comprehensively with accurate timing references. You should use Google Search to ground your responses when necessary.${userMemoryText}`,
    `Use this structure:`,
    `- Overview (2-3 lines)`,
    `- Timeline Highlights with [mm:ss–mm:ss] ranges and bullets`,
    `- Key Takeaways`,
    videoTitle ? `Video Title: ${videoTitle}` : ``,
    videoUrl ? `Video URL: ${videoUrl}` : ``,
    ``,
    `Transcript (with timestamps):`,
    formattedTranscript
  ].filter(Boolean).join('\n');

  try {
    chrome.runtime.sendMessage({ action: "startAIStream" });
    chrome.runtime.sendMessage({ action: "setMode", mode: "summarize" });

    const result = await model.generateContentStream({
      tools: [groundingTool],
      contents: [{ role: "user", parts: [{ text: prompt }] }],
      generationConfig: {
        temperature: 0.2,
        topK: 32,
        topP: 0.95,
        maxOutputTokens: 1024,
      }
    });

    for await (const chunk of result.stream) {
      const textChunk = chunk.candidates?.[0]?.content?.parts?.[0]?.text || "";
      if (textChunk) {
        chrome.runtime.sendMessage({ action: "appendAIMessageChunk", text: textChunk });
      }
    }

    chrome.runtime.sendMessage({ action: "endAIStream" });
    chrome.runtime.sendMessage({ 
      action: "appendUserMessage", 
      text: `//context-url: ${videoUrl}`,
      silent: true
    });

    // Reset summarize state
    chrome.runtime.sendMessage({ action: "resetSummarizeState" });
  } catch (e) {
    console.error("Error calling Gemini API:", e);
    chrome.runtime.sendMessage({ action: "displayError", error: e.message || String(e) });
    chrome.runtime.sendMessage({ action: "endAIStream" });
    chrome.runtime.sendMessage({ action: "resetSummarizeState" });
  }
}

export async function callGeminiTool(functionName, args) {
  const handler = toolHandlers[functionName];
  if (handler) {
    return await handler(args);
  }
  throw new Error(`Tool function ${functionName} not found.`);
}

export const about_user_read = {
  functionDeclarations: [{
    name: "about_user_read",
    description: "Reads the current user memory.",
    parameters: {
      type: "object",
      properties: {},
    },
  }],
};

export const about_user_write = {
  functionDeclarations: [{
    name: "about_user_write",
    description: "Adds new information to the user's comprehensive profile paragraph. This builds a continuous, detailed profile of the user.",
    parameters: {
      type: "object",
      properties: {
        memory: {
          type: "string",
          description: "New information about the user to add to their profile. Should be written in third person (e.g., 'User is a software developer from Canada who enjoys hiking.'). This will be integrated into the existing user profile paragraph."
        },
      },
      required: ["memory"],
    },
  }],
};

const toolHandlers = {
  about_user_read: async () => {
    const memory = await readUserMemory();
    // Return the user profile paragraph, or fall back to the old memories format for backward compatibility
    return {
      userProfile: memory.userProfile || '',
      // Include old format for backward compatibility
      memories: memory.memories || []
    };
  },
  about_user_write: async ({ memory }) => {
    // Read existing memory first
    const existingMemory = await readUserMemory();

    // Ensure memory is a string
    let newMemoryInfo = '';

    if (typeof memory === 'object') {
      // If somehow we got an object, try to convert it to a string
      try {
        newMemoryInfo = JSON.stringify(memory);
      } catch (e) {
        console.error("Error converting memory object to string:", e);
        newMemoryInfo = "User provided information that couldn't be processed.";
      }
    } else if (typeof memory === 'string') {
      // If it's already a string, use it directly
      newMemoryInfo = memory;
    } else {
      // Handle any other type
      newMemoryInfo = String(memory);
    }

    // Make sure the memory is properly formatted as a sentence
    if (newMemoryInfo && !newMemoryInfo.endsWith('.') && !newMemoryInfo.endsWith('!') && !newMemoryInfo.endsWith('?')) {
      newMemoryInfo += '.';
    }

    // Get existing user profile paragraph or create empty string
    const currentProfile = existingMemory.userProfile || '';

    // Only add new information if it's not empty and contains meaningful content
    if (newMemoryInfo && newMemoryInfo.trim().length > 0) {
      let updatedProfile = '';

      if (currentProfile.trim().length === 0) {
        // First memory entry - start the profile
        updatedProfile = newMemoryInfo;
      } else {
        // Add new information to existing profile
        // Check if the new information is already contained in the existing profile
        if (!currentProfile.toLowerCase().includes(newMemoryInfo.toLowerCase().replace(/\.$/, ''))) {
          updatedProfile = currentProfile + ' ' + newMemoryInfo;
        } else {
          // Information already exists, don't duplicate
          updatedProfile = currentProfile;
        }
      }

      // Create updated memory object with single paragraph format
      const updatedMemory = {
        ...existingMemory,
        userProfile: updatedProfile,
        // Keep the old memories array for backward compatibility during transition
        memories: existingMemory.memories || []
      };

      // Write the updated memory
      await writeUserMemory(updatedMemory);
      console.log("User memory updated with new info:", newMemoryInfo);
      console.log("Complete user profile:", updatedProfile);
    }

    return { success: true };
  },
};

// Export toolHandlers for testing
export { toolHandlers };