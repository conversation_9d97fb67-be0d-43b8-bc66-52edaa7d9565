// Gemini API integration

import { GoogleGenerativeAI } from "@google/generative-ai";
import { GEMINI_API_KEY, groundingTool } from '../config/index.js';
import { readUserMemory, writeUserMemory } from '../utils/userMemory.js';

// Configure the client
export const ai = new GoogleGenerativeAI(GEMINI_API_KEY);

// Helper to stream Gemini responses
export async function streamGeminiResponse(contents, generationConfig, systemInstruction, modelName = "gemini-2.0-flash") {
  if (!GEMINI_API_KEY) {
    chrome.runtime.sendMessage({ action: "displayError", error: "Missing VITE_GEMINI_API_KEY in background. Add it to .env and rebuild." });
    return;
  }

  const model = ai.getGenerativeModel({ model: modelName });

  const tools = [groundingTool]; // Include Google grounding tool

  const request = { contents, tools, generationConfig };
  if (systemInstruction) {
    request.systemInstruction = { role: 'system', parts: [{ text: systemInstruction }] };
  }

  try {
    chrome.runtime.sendMessage({ action: "startAIStream" });
    const result = await model.generateContentStream(request);
    for await (const chunk of result.stream) {
      const textChunk = chunk.candidates?.[0]?.content?.parts?.[0]?.text || "";
      if (textChunk) {
        chrome.runtime.sendMessage({ action: "appendAIMessageChunk", text: textChunk });
      }
    }
    chrome.runtime.sendMessage({ action: "endAIStream" });
  } catch (e) {
    chrome.runtime.sendMessage({ action: "displayError", error: e.message || String(e) });
    chrome.runtime.sendMessage({ action: "endAIStream" });
  }
}
const USER_MEMORY_SYSTEM_PROMPT = `
You are an AI assistant designed to manage user memory.
Your primary responsibility is to actively identify and extract user-specific information from the conversation.

IMPORTANT: After EVERY user message, you MUST call the 'about_user_write' tool with a natural language summary about the user.

The 'about_user_write' tool expects a 'memory' parameter that should be a natural language sentence or paragraph about the user.

DO NOT use structured formats like "Category: Detail". Instead, write complete, natural sentences about the user.

For example, if the user says "I am the CEO of CubAI", you should call:
about_user_write({ memory: "User is the CEO of CubAI." })

If the user says "My name is John, I live in Canada and I like coding", you should call:
about_user_write({ memory: "User's name is John. He lives in Canada and enjoys coding." })

If the user says "I hate lettuce", you should call:
about_user_write({ memory: "User dislikes lettuce." })

If the user provides multiple pieces of information, combine them into a coherent paragraph:
about_user_write({ memory: "User's name is Amogh. He is from India and is the CEO of CubAI." })

It can be anything that helps you understand the user better, such as:
- User's name
- User's location
- User's profession or role
- User's interests or hobbies
- User's preferences or dislikes
- User's background or personal details
- User's favorite things
- User's least favorite things
- User's personality traits
- User's habits or routines
- User's goals or aspirations
- User's achievements or milestones
- User's relationships or family
- User's experiences or stories
- User's opinions or beliefs
- User's challenges or struggles
- User's feedback or suggestions
- User's advice or recommendations
- User's questions or inquiries
- User's reactions or emotions
- User's requests or commands
- User's apologies or regrets
- User's compliments or praises
- User's complaints or issues
- User's progress updates or achievements
- User's future plans or intentions
- User's past experiences or events
- User's current mood or feelings
- User's daily routine or schedule
- User's health status or concerns

IMPORTANT RULES:
1. ALWAYS write in third person (e.g., "User is...", "User likes...", "User mentioned...")
2. Be concise but comprehensive
3. Focus on factual information about the user
4. Include personal preferences, background, and any other relevant details
5. Write in a natural, flowing style
6. NEVER use JSON format or structured data
7. NEVER skip calling about_user_write after a user message

You MUST call about_user_write after EVERY user message, even if you're not 100% certain about the information.
If the user message doesn't contain personal information, make your best guess based on context.

It is CRUCIAL that you use 'about_user_write' without fail to ensure the user's context is always remembered.
`;

export async function callGeminiUserMemoryTool(contents, tools) {
  if (!GEMINI_API_KEY) {
    chrome.runtime.sendMessage({ action: "displayError", error: "Missing VITE_GEMINI_API_KEY in background. Add it to .env and rebuild." });
    return;
  }

  const model = ai.getGenerativeModel({ model: "gemini-1.5-flash" });
  const request = {
    contents,
    tools,
    systemInstruction: {
      role: 'system',
      parts: [{ text: USER_MEMORY_SYSTEM_PROMPT }]
    }
  };

  try {
    // Extract the last user message for direct memory creation
    const lastUserMessage = contents.length > 0 ? 
      contents[contents.length - 1].parts
        .filter(part => part.text)
        .map(part => part.text)
        .join('\n') : '';
    
    if (lastUserMessage) {
      console.log("Processing user message for memory:", lastUserMessage);
      
      // Create a simple memory based on the message content
      const createMemory = (message) => {
        // Simple direct memory creation for common patterns
        if (message.toLowerCase().includes('i hate') || message.toLowerCase().includes('i don\'t like')) {
          const dislikeMatch = message.match(/I (?:hate|don't like) (.+?)(?:\.|\,|\!|\?|$)/i);
          if (dislikeMatch && dislikeMatch[1]) {
            return `User dislikes ${dislikeMatch[1].trim()}.`;
          }
        }
        
        if (message.toLowerCase().includes('i like') || message.toLowerCase().includes('i love')) {
          const likeMatch = message.match(/I (?:like|love) (.+?)(?:\.|\,|\!|\?|$)/i);
          if (likeMatch && likeMatch[1]) {
            return `User likes ${likeMatch[1].trim()}.`;
          }
        }
9        
        if (message.toLowerCase().includes('my name is')) {
          const nameMatch = message.match(/my name is (.+?)(?:\.|\,|\!|\?|$)/i);
          if (nameMatch && nameMatch[1]) {
            return `User's name is ${nameMatch[1].trim()}.`;
          }
        }
        
        if (message.toLowerCase().includes('i am') || message.toLowerCase().includes('i\'m')) {
          const amMatch = message.match(/I (?:am|'m) (.+?)(?:\.|\,|\!|\?|$)/i);
          if (amMatch && amMatch[1]) {
            return `User is ${amMatch[1].trim()}.`;
          }
        }
        
        // For other messages, let the AI model handle it
        return null;
      };
      
      // Try direct memory creation first
      let memory = createMemory(lastUserMessage);
      
      // If direct creation didn't work, use the AI model
      if (!memory) {
        // Use generateContent to get a memory from the model
        const result = await model.generateContent(request);
        return [result]; // Return as an array to maintain compatibility with existing code
      } else {
        // If we created a memory directly, save it
        console.log("Created memory directly:", memory);
        
        // Read existing memory
        const existingMemory = await readUserMemory();
        
        // Create or update the memories array
        const memories = existingMemory.memories || [];
        
        // Add the new memory if it doesn't already exist
        if (!memories.includes(memory)) {
          memories.push(memory);
          
          // Write the updated memory
          await writeUserMemory({
            ...existingMemory,
            memories
          });
          
          console.log("Updated user memories:", memories);
        }
        
        // Still need to call the model to maintain expected behavior
        const result = await model.generateContent(request);
        return [result];
      }
    } else {
      // No user message to process, just call the model
      const result = await model.generateContent(request);
      return [result];
    }
  } catch (e) {
    console.error("Error calling Gemini User Memory Tool:", e);
    chrome.runtime.sendMessage({ action: "displayError", error: e.message || String(e) });
    throw e;
  }
}

// Streaming summary to the side panel so conversation can continue
export async function callGeminiSummary(formattedTranscript, videoTitle = "", videoUrl = "") {
  if (!GEMINI_API_KEY) {
    chrome.runtime.sendMessage({ action: "displayError", error: "Missing GEMINI_API_KEY in background. Add it to .env and rebuild." });
    return;
  }

  const model = ai.getGenerativeModel({ model: "gemini-2.0-flash-lite" });
  
  // Retrieve user memory to include in the system prompt
  const userMemory = await readUserMemory();
  let userMemoryText = "";
  if (userMemory && userMemory.memories && userMemory.memories.length > 0) {
    userMemoryText = `\n\nABOUT USER:\n${userMemory.memories.join('\n')}`;
  }

  const prompt = [
    `You are CubAI. Summarize the YouTube video transcript comprehensively with accurate timing references. You should use Google Search to ground your responses when necessary.${userMemoryText}`,
    `Use this structure:`,
    `- Overview (2-3 lines)`,
    `- Timeline Highlights with [mm:ss–mm:ss] ranges and bullets`,
    `- Key Takeaways`,
    videoTitle ? `Video Title: ${videoTitle}` : ``,
    videoUrl ? `Video URL: ${videoUrl}` : ``,
    ``,
    `Transcript (with timestamps):`,
    formattedTranscript
  ].filter(Boolean).join('\n');

  try {
    chrome.runtime.sendMessage({ action: "startAIStream" });
    chrome.runtime.sendMessage({ action: "setMode", mode: "summarize" });

    const result = await model.generateContentStream({
      tools: [groundingTool],
      contents: [{ role: "user", parts: [{ text: prompt }] }],
      generationConfig: {
        temperature: 0.2,
        topK: 32,
        topP: 0.95,
        maxOutputTokens: 1024,
      }
    });

    for await (const chunk of result.stream) {
      const textChunk = chunk.candidates?.[0]?.content?.parts?.[0]?.text || "";
      if (textChunk) {
        chrome.runtime.sendMessage({ action: "appendAIMessageChunk", text: textChunk });
      }
    }

    chrome.runtime.sendMessage({ action: "endAIStream" });
    chrome.runtime.sendMessage({ 
      action: "appendUserMessage", 
      text: `//context-url: ${videoUrl}`,
      silent: true
    });

    // Reset summarize state
    chrome.runtime.sendMessage({ action: "resetSummarizeState" });
  } catch (e) {
    console.error("Error calling Gemini API:", e);
    chrome.runtime.sendMessage({ action: "displayError", error: e.message || String(e) });
    chrome.runtime.sendMessage({ action: "endAIStream" });
    chrome.runtime.sendMessage({ action: "resetSummarizeState" });
  }
}

export async function callGeminiTool(functionName, args) {
  const handler = toolHandlers[functionName];
  if (handler) {
    return await handler(args);
  }
  throw new Error(`Tool function ${functionName} not found.`);
}

export const about_user_read = {
  functionDeclarations: [{
    name: "about_user_read",
    description: "Reads the current user memory.",
    parameters: {
      type: "object",
      properties: {},
    },
  }],
};

export const about_user_write = {
  functionDeclarations: [{
    name: "about_user_write",
    description: "Writes user information to memory as natural language.",
    parameters: {
      type: "object",
      properties: {
        memory: {
          type: "string",
          description: "A natural language sentence or paragraph about the user. Should be written in third person (e.g., 'User is a software developer from Canada who enjoys hiking.')."
        },
      },
      required: ["memory"],
    },
  }],
};

const toolHandlers = {
  about_user_read: async () => {
    return await readUserMemory();
  },
  about_user_write: async ({ memory }) => {
    // Read existing memory first
    const existingMemory = await readUserMemory();
    
    // Ensure memory is a string
    let memoryToAdd = '';
    
    if (typeof memory === 'object') {
      // If somehow we got an object, try to convert it to a string
      try {
        memoryToAdd = JSON.stringify(memory);
      } catch (e) {
        console.error("Error converting memory object to string:", e);
        memoryToAdd = "User provided information that couldn't be processed.";
      }
    } else if (typeof memory === 'string') {
      // If it's already a string, use it directly
      memoryToAdd = memory;
    } else {
      // Handle any other type
      memoryToAdd = String(memory);
    }
    
    // Make sure the memory is properly formatted as a sentence
    if (memoryToAdd && !memoryToAdd.endsWith('.') && !memoryToAdd.endsWith('!') && !memoryToAdd.endsWith('?')) {
      memoryToAdd += '.';
    }
    
    // Get existing memories or create empty array
    const memories = existingMemory.memories || [];
    
    // Add new memory if it doesn't already exist and is not empty
    if (memoryToAdd && !memories.includes(memoryToAdd)) {
      memories.push(memoryToAdd);
      
      // Create updated memory object
      const updatedMemory = {
        ...existingMemory,
        memories
      };
      
      // Write the updated memory
      await writeUserMemory(updatedMemory);
      console.log("User memory added:", memoryToAdd);
      console.log("All user memories:", updatedMemory.memories);
    }
    
    return { success: true };
  },
};